-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Jun 11, 2025 at 11:56 AM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `visitpakistan`
--

-- --------------------------------------------------------

--
-- Table structure for table `destinations`
--

CREATE TABLE `destinations` (
  `id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `tid` int(11) NOT NULL,
  `rank` int(11) NOT NULL,
  `text` text NOT NULL,
  `videoLink` varchar(500) NOT NULL,
  `featureTitle1` varchar(255) NOT NULL,
  `featureText1` varchar(255) NOT NULL,
  `featureTitle2` varchar(255) NOT NULL,
  `featureText2` varchar(255) NOT NULL,
  `featureTitle3` varchar(255) NOT NULL,
  `featureText3` varchar(255) NOT NULL,
  `img` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `destinations`
--

INSERT INTO `destinations` (`id`, `title`, `tid`, `rank`, `text`, `videoLink`, `featureTitle1`, `featureText1`, `featureTitle2`, `featureText2`, `featureTitle3`, `featureText3`, `img`) VALUES
(1, 'Departure from Islamabad', 1, 2, 'good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good good ', 'https://www.youtube.com/embed/64RVtjiDTuY?si=a7OZ4ilcN41ZVZmQ', ' ✨ Accomodation', 'Nexted', '', '', '', '', ''),
(2, 'Journey to Hunza Valley', 1, 1, '', '', '', '', '', '', '', '', ''),
(3, 'Highest Cold Dessert', 1, 0, '', '', '', '', '', '', '', '', ''),
(4, 'Lahore', 2, 1, 'A City Of Gardans', '', '', '', '', '', '', '', '');

-- --------------------------------------------------------

--
-- Table structure for table `included`
--

CREATE TABLE `included` (
  `id` int(11) NOT NULL,
  `text` text NOT NULL,
  `tid` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `included`
--

INSERT INTO `included` (`id`, `text`, `tid`) VALUES
(1, '4WD', 1);

-- --------------------------------------------------------

--
-- Table structure for table `notincluded`
--

CREATE TABLE `notincluded` (
  `id` int(11) NOT NULL,
  `text` text NOT NULL,
  `tid` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `notincluded`
--

INSERT INTO `notincluded` (`id`, `text`, `tid`) VALUES
(1, 'ABcd', 1);

-- --------------------------------------------------------

--
-- Table structure for table `tour`
--

CREATE TABLE `tour` (
  `id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `persons` int(11) NOT NULL,
  `cost` int(11) NOT NULL,
  `highlight_destinations` text NOT NULL,
  `duration` int(11) NOT NULL,
  `icon` text NOT NULL,
  `text` text NOT NULL,
  `season` int(11) NOT NULL COMMENT '0=summer, 1=winter, 2=monsoon, 3=mid season',
  `iconColor` varchar(255) NOT NULL,
  `pdfLink` text NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `tour`
--

INSERT INTO `tour` (`id`, `title`, `persons`, `cost`, `highlight_destinations`, `duration`, `icon`, `text`, `season`, `iconColor`, `pdfLink`) VALUES
(1, 'Northern Areas Adventure', 4, 120, 'Hunza Valley • Skardu • Fairy Meadows', 9, '🏔️', '', 0, 'from-blue-500 to-purple-600', 'https://github.com/marketplace/models/azureml-mistral/Mistral-Large-2411/playground/code'),
(2, 'City Tour', 9, 1000, 'Lahore', 8, '🏛️', '', 1, 'from-orange-500 to-red-600', 'https://v0.dev/chat/travel-app-frontend-RNAoPw5Xd9J');

-- --------------------------------------------------------

--
-- Table structure for table `user`
--

CREATE TABLE `user` (
  `id` int(11) NOT NULL,
  `email` varchar(255) NOT NULL,
  `fullname` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `user`
--

INSERT INTO `user` (`id`, `email`, `fullname`, `password`) VALUES
(4, '<EMAIL>', 'Husnain Gilani', '$2y$10$3nR2j/y9i/aYdIWSCzSukeYC2shLGCMyqATwGTJkBL2/zfixIYjx2');

-- --------------------------------------------------------

--
-- Table structure for table `usertours`
--

CREATE TABLE `usertours` (
  `id` int(11) NOT NULL,
  `date` date NOT NULL DEFAULT current_timestamp(),
  `uid` int(11) NOT NULL,
  `tid` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `usertours`
--

INSERT INTO `usertours` (`id`, `date`, `uid`, `tid`) VALUES
(5, '2025-06-04', 4, 2),
(6, '2025-06-04', 4, 1),
(7, '2025-06-05', 4, 1);

--
-- Indexes for dumped tables
--

--
-- Indexes for table `destinations`
--
ALTER TABLE `destinations`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `included`
--
ALTER TABLE `included`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `notincluded`
--
ALTER TABLE `notincluded`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `tour`
--
ALTER TABLE `tour`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `user`
--
ALTER TABLE `user`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `usertours`
--
ALTER TABLE `usertours`
  ADD PRIMARY KEY (`id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `destinations`
--
ALTER TABLE `destinations`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `included`
--
ALTER TABLE `included`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `notincluded`
--
ALTER TABLE `notincluded`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `tour`
--
ALTER TABLE `tour`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `user`
--
ALTER TABLE `user`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `usertours`
--
ALTER TABLE `usertours`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
