<?php
include "includes/header.php";
include "includes/config.php";
if (!isset($_SESSION['user_id']) && $_SESSION['user_id'] == '') {
    header('Location: index.php');
}
?>
<body class="bg-gray-50 min-h-screen">
    <!-- Mobile Menu Button -->
    <div class="lg:hidden fixed top-4 left-4 z-50">
        <button id="mobile-menu-btn" onclick="toggleMobileMenu()" class="bg-white p-2 rounded-lg shadow-lg">
            <svg class="w-6 h-6 text-pakistan-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
            </svg>
        </button>
    </div>

    <!-- Mobile Menu Overlay -->
    <div id="mobile-menu-overlay" class="lg:hidden fixed inset-0 bg-black bg-opacity-50 z-40 hidden">
        <div class="fixed inset-y-0 left-0 w-64 bg-white shadow-lg transform -translate-x-full transition-transform duration-300 ease-in-out" id="mobile-menu">
            <div class="p-4 border-b">
                <h2 class="text-xl font-bold text-pakistan-green">🇵🇰 Visit Pakistan</h2>
            </div>
            <nav class="p-4">
                <ul class="space-y-4">
                      <li><a href="index.php" class="block text-gray-700 hover:text-pakistan-green">Home</a></li>
                    <li><button onclick="logout()" class="block w-full text-left text-red-600 hover:text-red-800">Logout</button></li>

                    <li><button  class="block w-full mt-8 text-left text-sm"><?php echo $_SESSION['fullname']; ?></button></li>
                </ul>
            </nav>
        </div>
    </div>

    <!-- Navigation -->
    <nav class="bg-white shadow-lg relative z-30">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0 flex items-center ml-12 lg:ml-0">
                        <h1 class="text-xl sm:text-2xl font-bold text-pakistan-green">🇵🇰 Visit Pakistan</h1>
                    </div>
                </div>
                <div class="hidden lg:flex items-center space-x-4">
                    <button class=" hover:text-pakistan-light px-3 py-2 rounded-md text-sm font-medium"><a href="index.php">Home</a></button>
                    <span class="text-gray-700 text-sm"><?php echo $_SESSION['fullname']; ?></span>
                    <button onclick="logout()" class="text-pakistan-green hover:text-pakistan-light px-3 py-2 rounded-md text-sm font-medium">Logout</button>
                </div>
                <!-- Mobile user info -->
                <div class="lg:hidden hidden flex items-center">
                    <span class="text-gray-700 text-sm"><?php echo $_SESSION['fullname']; ?></span>
                </div>
            </div>
        </div>
    </nav>

    <div class="max-w-7xl mx-auto py-4 sm:py-6 px-4 sm:px-6 lg:px-8">
        <!-- Dashboard Header -->
        <div class="mb-6 sm:mb-8">
            <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0">
                <div>
                    <h1 class="text-2xl sm:text-3xl font-bold text-gray-900">Your Travel Dashboard</h1>
                    <p class="mt-1 sm:mt-2 text-sm sm:text-base text-gray-600">Manage your tours and plan new adventures</p>
                </div>
                <button onclick="showNewTourModal()" class="w-full sm:w-auto bg-pakistan-green hover:bg-pakistan-light text-white px-4 sm:px-6 py-2 sm:py-3 rounded-lg font-semibold transition duration-300 text-sm sm:text-base">
                    + New Tour
                </button>
            </div>
        </div>

        

        <!-- Previous Tours -->
        <div class="bg-white shadow overflow-hidden sm:rounded-md">
            <div class="px-4 py-5 sm:px-6">
                <h3 class="text-base sm:text-lg leading-6 font-medium text-gray-900">Your Tours</h3>
                <p class="mt-1 max-w-2xl text-xs sm:text-sm text-gray-500">A list of all your completed and upcoming tours</p>
            </div>
            <ul class="divide-y divide-gray-200">
                <!-- Tour 1 -->
                <?php
                $uid = $_SESSION['user_id'];
                $query = "SELECT ut.tid, ut.date, t.* FROM usertours ut 
                         JOIN tour t ON ut.tid = t.id 
                         WHERE ut.uid = $uid
                         ORDER BY ut.id DESC";
                $result = mysqli_query($db, $query);
                
                if(mysqli_num_rows($result) == 0) {
                    echo '<li class="px-4 py-8 text-center">
                        <div class="text-gray-500 text-sm sm:text-base">
                            <p class="mb-2">You haven\'t booked any tours yet.</p>
                            <p class="text-pakistan-green">Click on "+ New Tour" to start planning your adventure!</p>
                        </div>
                    </li>';
                } else {
                    while($row = mysqli_fetch_assoc($result)) {
                        $formatted_date = date("F j, Y", strtotime($row['date']));
                        echo '<li>
                            <div class="px-4 py-4 flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-12 w-12 sm:h-16 sm:w-16">
                                        <div class="h-12 w-12 sm:h-16 sm:w-16 rounded-lg bg-gradient-to-r ' . $row['iconColor'] . ' flex items-center justify-center">
                                            <span class="text-white text-lg sm:text-2xl">' . $row['icon'] . '</span>
                                        </div>
                                    </div>
                                    <div class="ml-3 sm:ml-4 flex-1">
                                        <div class="text-sm sm:text-base font-medium text-gray-900">' . $row['title'] . '</div>
                                        <div class="text-xs sm:text-sm text-gray-500">' . $row['highlight_destinations'] . '</div>
                                        <div class="text-xs text-gray-400 mt-1">' . $formatted_date . ' • ' . $row['duration'] . ' days</div>
                                    </div>
                                </div>
                                <div class="flex items-center justify-between sm:justify-end space-x-3 sm:space-x-4">
                                    <button onclick="viewTourPlan(' . $row['tid'] . ')" class="text-pakistan-green hover:text-pakistan-light font-medium text-xs sm:text-sm">View Plan</button>
                                </div>
                            </div>
                        </li>';
                    }
                }
                ?>
                
            </ul>
        </div>

        <!-- Quick Actions (Mobile) -->
       
    </div>

    <!-- New Tour Modal -->
    <div id="newTourModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50 p-4">
        <div class="flex items-center justify-center min-h-screen">
            <div class="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-screen overflow-y-auto">
                <div class="sticky top-0 bg-white px-4 z-90 sm:px-6 py-4 border-b flex justify-between items-center">
                    <h2 class="text-xl sm:text-2xl font-bold text-gray-900">Plan New Tour</h2>
                    <button onclick="closeNewTourModal()" class="text-gray-400 hover:text-gray-600">
                        <span class="text-2xl">&times;</span>
                    </button>
                </div>
                
                <div class="p-4 sm:p-6">
                    <form onsubmit="handleNewTour(event)">
                        <div class="grid grid-cols-1 gap-6 sm:gap-8">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Season</label>
                                <select name="season" id="season" required onchange="calculateMinBudget()" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-pakistan-green">
                                    <option value="">Select Season...</option>
                                    <option value="spring">Spring (March-May)</option>
                                    <option value="summer">Summer (May-September)</option>
                                    <option value="autumn">Autumn (September-November)</option>
                                    <option value="winter">Winter (December-February)</option>
                                </select>
                            </div>
                            
                           

                            <div >
                                <label class="block text-sm font-medium text-gray-700 mb-2">Are you willing to drive? Avg Time: Around 5 Hours</label>
                                <div class="grid grid-cols-2 gap-2">
                                    <label class="flex items-center">
                                        <input type="radio" name="willing_to_drive" value="yes" required onchange="calculateMinBudget()" class="border-gray-300 text-pakistan-green focus:ring-pakistan-green">
                                        <span class="ml-2 text-sm">Yes, I Am Willing to drive (Better experiences and more better views, cheaper)</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="radio" name="willing_to_drive" value="no" required onchange="calculateMinBudget()" class="border-gray-300 text-pakistan-green focus:ring-pakistan-green">
                                        <span class="ml-2 text-sm">No, I prefer not to drive at all (Less tiring, more expensive)</span>
                                    </label>
                                </div>
                            </div>
                             <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Duration (Days)</label>
                                <select name="duration" id="duration" required onchange="calculateMinBudget()" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-pakistan-green">
                                    <option value="">Select Duration...</option>
                                    <option value="3">3 Days</option>
                                    <option value="5">5 Days</option>
                                    <option value="7">7 Days</option>
                                    <option value="10">10 Days</option>
                                    <option value="14">14 Days</option>
                                </select>
                            </div>
                             <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Tour Type</label>
                                <div class="grid grid-cols-2 gap-2">
                                    <label class="flex items-center">
                                        <input type="radio" name="tour_type" value="diverse" required class="border-gray-300 text-pakistan-green focus:ring-pakistan-green">
                                        <span class="ml-2 text-sm">Diverse (Multiple regions/cultures, good for big tours)</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="radio" name="tour_type" value="focused" required  class="border-gray-300 text-pakistan-green focus:ring-pakistan-green">
                                        <span class="ml-2 text-sm">Focused (Focused to one region majorly, good for small tours)</span>
                                    </label>
                                </div>
                            </div>
                             <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Tour Action</label>
                                <div class="grid grid-cols-2 gap-2">
                                    <label class="flex items-center">
                                        <input type="radio" name="tour_action" value="diverse" required class="border-gray-300 text-pakistan-green focus:ring-pakistan-green">
                                        <span class="ml-2 text-sm">Adventurous (Off road, places with less hotels etc) </span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="radio" name="tour_action" value="focused" required  class="border-gray-300 text-pakistan-green focus:ring-pakistan-green">
                                        <span class="ml-2 text-sm">Not Adventurous (Good for families, places with more hotels and accessability etc) </span>
                                    </label>
                                </div>
                            </div>
                            <div id="focused-region" style="display: none;">
                                <label class="block text-sm font-medium text-gray-700 mb-2">Preferred Region</label>
                                <select name="focused_region" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-pakistan-green">
                                    <option value="">Select Region...</option>
                                    <option value="north">North (Mountains, Valleys, Lakes)</option>
                                    <option value="south">South (Beaches, Historical Sites)</option>
                                    <option value="central">Central (Cultural Heritage, Urban Life)</option>
                                    <option value="west">West (Desert Adventures, Ancient Ruins)</option>
                                </select>
                            </div>

                            <script>
                                document.querySelectorAll('input[name="tour_type"]').forEach(radio => {
                                    radio.addEventListener('change', function() {
                                        const focusedRegion = document.getElementById('focused-region');
                                        focusedRegion.style.display = this.value === 'focused' ? 'block' : 'none';
                                    });
                                });
                            </script>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Budget Range</label>
                                <select name="budget" id="budget" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-pakistan-green">
                                    <option value="">Select Budget Range...</option>
                                </select>
                                <p id="minBudgetInfo" class="text-xs text-gray-500 mt-1"></p>
                            </div>
                       
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Travelers</label>
                                <div id="travelers-container" class="space-y-3">
                                    <div class="traveler-entry border border-gray-300 rounded-md p-4">
                                        <div class="grid grid-cols-3 gap-3">
                                            <div>
                                                <label class="block text-xs text-gray-600 mb-1">Age</label>
                                                <input type="number" name="traveler_age[]" min="0" max="120" required onchange="calculateMinBudget()" class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-pakistan-green">
                                            </div>
                                            <div>
                                                <label class="block text-xs text-gray-600 mb-1">Gender</label>
                                                <select name="traveler_gender[]" required class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-pakistan-green">
                                                    <option value="">Select...</option>
                                                    <option value="male">Male</option>
                                                    <option value="female">Female</option>
                                                    <option value="other">Other</option>
                                                </select>
                                            </div>
                                            <div class="flex items-end">
                                                <button type="button" onclick="removeTraveler(this)" class="text-red-500 hover:text-red-700 px-2 py-2">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <button type="button" onclick="addTraveler()" class="mt-3 text-pakistan-green hover:text-pakistan-light text-sm flex items-center">
                                    <i class="fas fa-plus mr-2"></i> Add Traveler
                                </button>
                                <script>
                                    function addTraveler() {
                                        const container = document.getElementById('travelers-container');
                                        const template = container.children[0].cloneNode(true);
                                        container.appendChild(template);
                                        calculateMinBudget();
                                    }
                                    
                                    function removeTraveler(button) {
                                        const container = document.getElementById('travelers-container');
                                        if (container.children.length > 1) {
                                            button.closest('.traveler-entry').remove();
                                            calculateMinBudget();
                                        }
                                    }

                                    function calculateMinBudget() {
                                        const duration = parseInt(document.getElementById('duration').value) || 0;
                                        const season = document.getElementById('season').value;
                                        const willingToDrive = document.getElementById('willing_to_drive').value;
                                        const travelers = document.querySelectorAll('.traveler-entry');
                                        let totalMinBudget = 0;
                                        
                                        // Base cost per day in dollars
                                        let baseCostPerDay = 50;
                                        
                                        // Season-based multiplier
                                        const seasonMultipliers = {
                                            'spring': 1.2,    // Peak season
                                            'summer': 1.1,    // High season
                                            'autumn': 1.0,    // Moderate season
                                            'winter': 0.9     // Low season
                                        };
                                        
                                        if (season && seasonMultipliers[season]) {
                                            baseCostPerDay *= seasonMultipliers[season];
                                        }
                                        
                                        // Calculate for each traveler
                                        travelers.forEach(traveler => {
                                            const age = parseInt(traveler.querySelector('input[type="number"]').value) || 0;
                                            
                                            // Age-based multiplier (non-linear)
                                            let ageMultiplier = 1;
                                            if (age < 12) ageMultiplier = 0.7;
                                            else if (age < 18) ageMultiplier = 0.9;
                                            else if (age > 60) ageMultiplier = 1.2;
                                            
                                            // Calculate individual cost with exponential scaling
                                            const individualCost = baseCostPerDay * ageMultiplier * Math.pow(1.1, Math.floor(duration/3));
                                            totalMinBudget += individualCost * duration;
                                        });
                                        
                                        // Group size discount
                                        const groupDiscount = Math.max(0, 1 - (travelers.length * 0.05));
                                        totalMinBudget *= groupDiscount;
                                        
                                        // Duration-based scaling
                                        totalMinBudget *= (1 + Math.log10(duration || 1));

                                        // Driving preference adjustment
                                        if (willingToDrive === 'yes') {
                                            totalMinBudget *= 0.8; // 20% reduction for self-driving
                                        } else if (willingToDrive === 'no') {
                                            totalMinBudget *= 1.2; // 20% increase for arranged transport
                                        }
                                        
                                        // Round to nearest 10 dollars
                                        totalMinBudget = Math.ceil(totalMinBudget / 10) * 10;
                                        
                                        // Update budget select options
                                        const budgetSelect = document.getElementById('budget');
                                        budgetSelect.innerHTML = '<option value="">Select Budget Range...</option>';
                                        
                                        const ranges = [
                                            [totalMinBudget, totalMinBudget * 1.5],
                                            [totalMinBudget * 1.5, totalMinBudget * 2],
                                            [totalMinBudget * 2, totalMinBudget * 3]
                                        ];
                                        
                                        ranges.forEach(([min, max]) => {
                                            const option = document.createElement('option');
                                            option.value = `${min}-${max}`;
                                            option.textContent = `$ ${Math.round(min).toLocaleString()} - ${Math.round(max).toLocaleString()}`;
                                            budgetSelect.appendChild(option);
                                        });
                                        
                                        // Update info text
                                        document.getElementById('minBudgetInfo').textContent = 
                                            `Minimum recommended budget: $ ${Math.round(totalMinBudget).toLocaleString()}`;
                                    }
                                </script>
                            </div>
                          
                           
                           
                            <div class="mt-6 flex flex-col sm:flex-row justify-end space-y-3 sm:space-y-0 sm:space-x-3">
                                <button type="button" onclick="closeNewTourModal()" class="w-full sm:w-auto px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 text-sm sm:text-base">
                                    Cancel
                                </button>
                                <button type="submit" class="w-full sm:w-auto px-6 py-2 bg-pakistan-green text-white rounded-md hover:bg-pakistan-light transition duration-300 text-sm sm:text-base">
                                    Create Tour Plan
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>


    <script src="includes/media/central.js"></script>
    <script src="includes/media/dashboard.js"></script>
<?php
include "includes/footer.php";
?>

  <!-- <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Preferred Transport Between Cities</label>
                                <div class="grid grid-cols-2 gap-4">
                                    <div class="border-gray-300 border rounded-md p-4 cursor-pointer option-item transition-colors duration-200 shadow-sm" data-type="transport" onclick="selectOption(this, 'car', 'transport')">
                                        <input type="radio" name="transport" value="car" class="hidden" required>
                                        <div class="text-center rounded-lg p-3">
                                            <i class="fas fa-car text-3xl mb-3 text-pakistan-green"></i>
                                            <h3 class="font-semibold text-gray-800">Car (Drive Yourself)</h3>
                                            <div class="mt-3 text-xs space-y-1">
                                                <p class="text-green-600 flex items-center justify-center"><i class="fas fa-check mr-1"></i>Easy Transport Inside City</p>
                                                <p class="text-green-600 flex items-center justify-center"><i class="fas fa-check mr-1"></i>Waaay More Beautiful Views</p>
                                                <p class="text-green-600 flex items-center justify-center"><i class="fas fa-check mr-1"></i>More Experiences</p>
                                                <p class="text-green-600 flex items-center justify-center"><i class="fas fa-check mr-1"></i>Less Transport Cost And More Spending On Other Things</p>
                                                <p class="text-red-500 flex items-center justify-center"><i class="fas fa-times mr-1"></i>Tiring To Drive</p>

                                            </div>
                                        </div>
                                    </div>
                                    <div class="border-gray-300 border rounded-md p-4 cursor-pointer option-item transition-colors duration-200 shadow-sm" data-type="transport" onclick="selectOption(this, 'airplane', 'transport')">
                                        <input type="radio" name="transport" value="airplane" class="hidden" required>
                                        <div class="text-center rounded-lg p-3">
                                            <i class="fas fa-plane text-3xl mb-3 text-pakistan-green"></i>
                                            <h3 class="font-semibold text-gray-800">Airplane</h3>
                                            <div class="mt-3 text-xs space-y-1">
                                                <p class="text-green-600 flex items-center justify-center"><i class="fas fa-check mr-1"></i>Not Tiring</p>
                                                
                                                <p class="text-red-500 flex items-center justify-center"><i class="fas fa-check mr-1"></i>Not As Many Beautiful Views</p>
                                    <p class="text-red-500 flex items-center justify-center"><i class="fas fa-check mr-1"></i>Higher Cost</p>
                                                <p class="text-red-500 flex items-center justify-center"><i class="fas fa-times mr-1"></i>Not that many experiences</p>

                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div> -->


                             <!-- <script>
                                function selectOption(element, value, type) {
                                    document.querySelectorAll(`.option-item[data-type="${type}"]`).forEach(option => {
                                        option.classList.remove('bg-gray-50', 'shadow-md');
                                        option.querySelector('input[type="radio"]').checked = false;
                                    });
                                    element.classList.add('bg-gray-50', 'shadow-md');
                                    element.querySelector('input[type="radio"]').checked = true;
                                }
                            </script> -->