<?php
include '../includes/config.php';
// API endpoint to process new tour form data and get AI recommendation
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Sanitize and capture all form inputs In Shaa Allah
    $season = isset($_POST['season']) ? trim($_POST['season']) : '';
    $duration = isset($_POST['duration']) ? intval($_POST['duration']) : 0;
    $budget = isset($_POST['budget']) ? trim($_POST['budget']) : '';
    $special_requirements = isset($_POST['special_requirements']) ? trim($_POST['special_requirements']) : '';
    $willing_to_drive = isset($_POST['willing_to_drive']) ? trim($_POST['willing_to_drive']) : '';
    $tour_type = isset($_POST['tour_type']) ? trim($_POST['tour_type']) : '';
    $focused_region = isset($_POST['focused_region']) ? trim($_POST['focused_region']) : '';
    $tour_action = isset($_POST['tour_action']) ? trim($_POST['tour_action']) : '';
    // Process travelers data (dynamic array)
    $travelers = [];
    if (isset($_POST['traveler_age']) && isset($_POST['traveler_gender'])) {
        $ages = $_POST['traveler_age'];
        $genders = $_POST['traveler_gender'];

        for ($i = 0; $i < count($ages); $i++) {
            if (!empty($ages[$i]) && !empty($genders[$i])) {
                $travelers[] = [
                    'age' => intval($ages[$i]),
                    'gender' => trim($genders[$i])
                ];
            }
        }
    }
    // Build comprehensive user requirements string In Shaa Allah
    $userRequirements = "User Tour Requirements:\n";

    // Season preference
    if (!empty($season)) {
        $seasonNames = [
            'spring' => 'Spring (March-May)',
            'summer' => 'Summer (May-September)',
            'autumn' => 'Autumn (September-November)',
            'winter' => 'Winter (December-February)'
        ];
        $userRequirements .= "- Preferred Season: " . ($seasonNames[$season] ?? $season) . "\n";
    }

    // Duration
    if ($duration > 0) {
        $userRequirements .= "- Duration: {$duration} days\n";
    }

    // Budget
    if (!empty($budget)) {
        $userRequirements .= "- Budget Range: {$budget}\n";
    }
    if (!empty($tour_type)) {
        $userRequirements .= "- Tour Type: {$tour_type}\n";
    }
    if (!empty($focused_region)) {
        $userRequirements .= "- Focused Region: {$focused_region}\n";
    }
    if (!empty($tour_action)) {
        $userRequirements .= "- Tour Action: {$tour_action}\n";
    }
    if (!empty($willing_to_drive)) {
        $userRequirements .= "- Willing to Drive: {$willing_to_drive}\n";
    }
    // Travelers information
    if (!empty($travelers)) {
        $userRequirements .= "- Number of Travelers: " . count($travelers) . "\n";
        $userRequirements .= "- Traveler Details:\n";
        foreach ($travelers as $index => $traveler) {
            $userRequirements .= "  * Traveler " . ($index + 1) . ": Age {$traveler['age']}, Gender: {$traveler['gender']}\n";
        }
    }

    // Special requirements
    if (!empty($special_requirements)) {
        $userRequirements .= "- Special Requirements: {$special_requirements}\n";
    }

    // Add fallback if no requirements provided
    if (trim($userRequirements) === "User Tour Requirements:") {
        $userRequirements .= "- No specific requirements provided. Please suggest a popular tour.\n";
    }

    //fetch tours from db In Shaa Allah

    // Get user ID from session
    $userId = $_SESSION['user_id'];

    // First try to fetch tours not in user's history
    $sql = "SELECT t.*, GROUP_CONCAT(d.title ORDER BY d.rank DESC SEPARATOR ' • ') as destinations
            FROM tour t
            LEFT JOIN destinations d ON t.id = d.tid
            WHERE t.id NOT IN (SELECT tid FROM usertours WHERE uid = ?)
            GROUP BY t.id";
    $stmt = $db->prepare($sql);
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $result = $stmt->get_result();

    // If no results found, fetch any tour
    if ($result->num_rows == 0) {
        $sql = "SELECT t.*, GROUP_CONCAT(d.title ORDER BY d.rank DESC SEPARATOR ' • ') as destinations
                FROM tour t
                LEFT JOIN destinations d ON t.id = d.tid
                GROUP BY t.id
                LIMIT 1";
        $result = $db->query($sql);
    }

    // Build tours information string
    $toursInfo = "Available tours:\n";
    while($tour = $result->fetch_assoc()) {
        $toursInfo .= "- {$tour['title']}, id {$tour['id']} ({$tour['duration']} days, {$tour['persons']} persons, $" . $tour['cost'] . "/person)\n";
        $toursInfo .= "  Destinations: {$tour['destinations']}\n";
    }

    // Prepare data for the API request
    $data = array(
        'model' => 'mistral-ai/Mistral-Large-2411',
        'messages' => array(
            array(
                'role' => 'system',
                'content' => "You are a helpful travel assistant that helps plan tours in Pakistan. Here are the current available tours:\n\n$toursInfo\n\nAnalyze the user's detailed requirements and suggest the most suitable tour. Consider factors like:\n- Season preferences and weather conditions\n- Duration matching\n- Budget compatibility\n- Group size and traveler demographics\n- Special requirements\n\nIn Shaa Allah, return only the tour ID number that best matches the requirements, no other text."
            ),
            array(
                'role' => 'user',
                'content' => $userRequirements
            )
        ),
        'temperature' => 0.2,
        'top_p' => 1
    );

    // Initialize cURL session
    $ch = curl_init('https://models.github.ai/inference/chat/completions');
    
    // Set cURL options
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
        'Content-Type: application/json',
        'Authorization: Bearer ****************************************' 
    ));

    // Execute cURL request
    $response = curl_exec($ch);
    
    // Check for errors
    if(curl_errno($ch)) {
        echo json_encode(array(
            'success' => false,
            'message' => 'Curl error: ' . curl_error($ch)
        ));
        exit;
    }
    
    curl_close($ch);

    // Parse response and return only the first choice message
    $responseData = json_decode($response, true);
    $message = isset($responseData['choices'][0]['message']['content']) 
        ? $responseData['choices'][0]['message']['content'] 
        : 'No response generated';

    // Return response
    echo json_encode(array(
        'success' => true,
        'response' => $message
    ));

}
?>